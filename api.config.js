// OpenAPI 配置文件 - 统一管理所有 API 相关配置
// 这是 JavaScript 版本，用于 Node.js 脚本

const config = {
	// Swagger 文档地址
	swaggerUrl: "http://*************:6039/v3/api-docs",

	// API 基础地址
	baseUrl: "http://*************:6039", // 开发环境接口地址

	// 生成配置
	generate: {
		// 生成文件的输出目录
		serversPath: "./src/services",
		// 请求库路径 - 使用原有的请求系统
		requestLibPath: "import request from '@/utils/request'",
		// 项目名称
		projectName: "api",
		// 命名空间
		namespace: "API",
		// 生成的接口文件配置
		apiPrefix: "",
		// 是否生成 mock 数据
		mock: false,
	},

	// 请求配置
	requestConfig: {
		timeout: 10000,
		headers: {
			"Content-Type": "application/json",
		},
	},

	// 认证配置
	auth: {
		type: "bearer", // 'bearer' | 'basic' | 'apikey'
		tokenKey: "token", // localStorage 中存储 token 的 key
		headerName: "Authorization", // 请求头名称
	},
};

module.exports = config;
