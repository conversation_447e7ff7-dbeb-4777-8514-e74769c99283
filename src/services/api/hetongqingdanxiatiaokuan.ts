// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 编辑条款，更新指定合同的所有条款 编辑条款，更新指定合同的所有条款 PUT /pactListClause/ */
export async function edit17(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.edit17Params,
  body: API.PactListClauseBo[],
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/pactListClause/", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增条款，为指定合同添加多条条款 新增条款，为指定合同添加多条条款 POST /pactListClause/ */
export async function add17(
  body: API.PactListClauseBo[],
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/pactListClause/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除条款 删除条款 DELETE /pactListClause/${param0} */
export async function remove19(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove19Params,
  options?: { [key: string]: any }
) {
  const { clauseIds: param0, ...queryParams } = params;
  return request<API.RVoid>(`/pactListClause/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取清单下条款 根据合同清单ID，获取清单下条款所有未删除条款 GET /pactListClause/clauses/${param0} */
export async function getClausesByPactId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getClausesByPactIdParams,
  options?: { [key: string]: any }
) {
  const { pactId: param0, ...queryParams } = params;
  return request<API.RListPactListClauseVo>(
    `/pactListClause/clauses/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}
