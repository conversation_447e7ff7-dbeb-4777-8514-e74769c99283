declare namespace API {
  type allocatedListParams = {
    user: SysUserBo;
    pageQuery: PageQuery;
  };

  type attachParams = {
    bo: KnowledgeAttachBo;
    pageQuery: PageQuery;
    kid: string;
  };

  type authRoleParams = {
    /** 用户ID */
    userId: number;
  };

  type AvatarVo = {
    /** 头像地址 */
    imgUrl?: string;
  };

  type batchGenCodeParams = {
    /** 表名串 */
    tableIdStr: string;
  };

  type CacheListInfoVo = {
    info?: Record<string, any>;
    dbSize?: number;
    commandStats?: Record<string, any>[];
  };

  type cancelAuthUserAllParams = {
    /** 角色ID */
    roleId: number;
    /** 用户ID串 */
    userIds: number[];
  };

  type CaptchaVo = {
    /** 是否开启验证码 */
    captchaEnabled?: boolean;
    uuid?: string;
    /** 验证码图片 */
    img?: string;
  };

  type ChatConfigBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主键 */
    id: number;
    /** 配置类型 */
    category: string;
    /** 配置名称 */
    configName: string;
    /** 配置值 */
    configValue: string;
    /** 说明 */
    configDict: string;
    /** 备注 */
    remark: string;
    /** 更新IP */
    updateIp: string;
  };

  type ChatConfigVo = {
    /** 主键 */
    id?: number;
    /** 配置类型 */
    category?: string;
    /** 配置名称 */
    configName?: string;
    /** 配置值 */
    configValue?: string;
    /** 说明 */
    configDict?: string;
    /** 备注 */
    remark?: string;
    /** 更新IP */
    updateIp?: string;
  };

  type ChatMessageBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主键 */
    id: number;
    /** 用户id */
    userId: number;
    /** 消息内容 */
    content: string;
    /** 会话id */
    sessionId: number;
    /** 对话角色 */
    role: string;
    /** 扣除金额 */
    deductCost: number;
    /** 累计 Tokens */
    totalTokens: number;
    /** 模型名称 */
    modelName: string;
    /** 备注 */
    remark: string;
  };

  type ChatMessageVo = {
    /** 主键 */
    id?: number;
    /** 用户id */
    userId?: number;
    /** 会话id */
    sessionId?: number;
    /** 消息内容 */
    content?: string;
    /** 对话角色 */
    role?: string;
    /** 扣除金额 */
    deductCost?: number;
    /** 累计 Tokens */
    totalTokens?: number;
    /** 模型名称 */
    modelName?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type ChatModel = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主键 */
    id?: number;
    /** 模型分类 */
    category?: string;
    /** 模型名称 */
    modelName?: string;
    /** 模型描述 */
    modelDescribe?: string;
    /** 模型图标 */
    icon?: string;
    /** 模型价格 */
    modelPrice?: number;
    /** 计费类型 */
    modelType?: string;
    /** 是否显示 */
    modelShow?: string;
    /** 系统提示词 */
    systemPrompt?: string;
    /** 请求地址 */
    apiHost?: string;
    /** 密钥 */
    apiKey?: string;
    /** 备注 */
    remark?: string;
  };

  type ChatModelBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主键 */
    id: number;
    /** 模型分类 */
    category: string;
    /** 模型名称 */
    modelName: string;
    /** 模型描述 */
    modelDescribe: string;
    /** 模型图标 */
    icon?: string;
    /** 模型价格 */
    modelPrice: number;
    /** 计费类型 */
    modelType: string;
    /** 是否显示 */
    modelShow: string;
    /** 系统提示词 */
    systemPrompt?: string;
    /** 请求地址 */
    apiHost: string;
    /** 密钥 */
    apiKey: string;
    /** 备注 */
    remark: string;
    /** 角色ID */
    roleId?: number;
  };

  type ChatModelVo = {
    /** 主键 */
    id?: number;
    /** 模型分类 */
    category?: string;
    /** 模型名称 */
    modelName?: string;
    /** 模型描述 */
    modelDescribe?: string;
    /** 模型图标 */
    icon?: string;
    /** 模型价格 */
    modelPrice?: number;
    /** 计费类型 */
    modelType?: string;
    /** 是否显示 */
    modelShow?: string;
    /** 系统提示词 */
    systemPrompt?: string;
    /** 请求地址 */
    apiHost?: string;
    /** 密钥 */
    apiKey?: string;
    /** 备注 */
    remark?: string;
  };

  type ChatPayOrderBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主键 */
    id: number;
    /** 订单编号 */
    orderNo: string;
    /** 订单名称 */
    orderName: string;
    /** 金额 */
    amount: number;
    /** 支付状态 */
    paymentStatus: string;
    /** 支付方式 */
    paymentMethod: string;
    /** 用户ID */
    userId: number;
    /** 备注 */
    remark: string;
  };

  type ChatPayOrderVo = {
    /** 主键 */
    id?: number;
    /** 订单编号 */
    orderNo?: string;
    /** 订单名称 */
    orderName?: string;
    /** 金额 */
    amount?: number;
    /** 支付状态 */
    paymentStatus?: string;
    /** 支付方式 */
    paymentMethod?: string;
    /** 用户ID */
    userId?: number;
    /** 备注 */
    remark?: string;
  };

  type ChatRequest = {
    messages: Message[];
    model: string;
    /** 提示词 */
    prompt?: string;
    /** 系统提示词 */
    sysPrompt?: string;
    /** 是否开启流式对话 */
    stream?: boolean;
    /** 知识库id */
    kid?: string;
    /** 用户id */
    userId?: number;
    /** 会话id */
    sessionId?: number;
    /** 应用ID */
    appId?: string;
    /** 对话角色 */
    role?: string;
  };

  type ChatSessionBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主键 */
    id: number;
    /** 用户id */
    userId?: number;
    /** 会话标题 */
    sessionTitle?: string;
    /** 会话内容 */
    sessionContent?: string;
    /** 备注 */
    remark?: string;
  };

  type ChatSessionVo = {
    /** 主键 */
    id?: number;
    /** 用户id */
    userId?: number;
    /** 会话标题 */
    sessionTitle?: string;
    /** 会话内容 */
    sessionContent?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type columnListParams = {
    /** 表ID */
    tableId: number;
  };

  type dataListParams = {
    genTable: GenTable;
    pageQuery: PageQuery;
  };

  type deptTreeParams = {
    dept: SysDeptBo;
  };

  type DeptTreeSelectVo = {
    /** 选中部门列表 */
    checkedKeys?: number[];
    /** 下拉树结构列表 */
    depts?: TreeLong[];
  };

  type dictTypeParams = {
    /** 字典类型 */
    dictType: string;
  };

  type download1Params = {
    /** OSS对象ID */
    ossId: number;
  };

  type downloadParams = {
    /** 表名 */
    tableId: number;
  };

  type edit17Params = {
    pactId: number;
  };

  type EmailLoginBody = {
    /** 租户ID */
    tenantId: string;
    /** 邮箱 */
    email: string;
    /** 邮箱code */
    emailCode: string;
  };

  type EmailRequest = {
    /** 账号 */
    username: string;
  };

  type excludeChildParams = {
    /** 部门ID */
    deptId: number;
  };

  type export10Params = {
    dictData: SysDictDataBo;
  };

  type export11Params = {
    config: SysConfigBo;
  };

  type export12Params = {
    operLog: SysOperLogBo;
  };

  type export13Params = {
    logininfor: SysLogininforBo;
  };

  type export14Params = {
    bo: KnowledgeInfoBo;
  };

  type export15Params = {
    bo: ChatConfigBo;
  };

  type export1Params = {
    bo: SysTenantPackageBo;
  };

  type export2Params = {
    bo: ChatSessionBo;
  };

  type export3Params = {
    role: SysRoleBo;
  };

  type export4Params = {
    post: SysPostBo;
  };

  type export5Params = {
    bo: ChatPayOrderBo;
  };

  type export6Params = {
    bo: SysNoticeStateBo;
  };

  type export7Params = {
    bo: ChatModelBo;
  };

  type export8Params = {
    bo: ChatMessageBo;
  };

  type export9Params = {
    dictType: SysDictTypeBo;
  };

  type exportUsingPOSTParams = {
    user: SysUserBo;
  };

  type feedParams = {
    taskId: string;
  };

  type fetchParams = {
    id: string;
  };

  type forceLogoutParams = {
    /** token值 */
    tokenId: string;
  };

  type fragmentListParams = {
    bo: KnowledgeFragmentBo;
    pageQuery: PageQuery;
    docId: string;
  };

  type FunctionCall = {
    /** 方法名 */
    name?: string;
    /** 方法参数 */
    arguments?: string;
  };

  type genCodeParams = {
    /** 表名 */
    tableName: string;
  };

  type GenerateLuma = {
    aspect_ratio?: string;
    expand_prompt?: boolean;
    image_url?: string;
    user_prompt?: string;
  };

  type GenerateLyric = {
    /** 歌词提示词 */
    prompt?: string;
    /** 回调地址 */
    notify_hook?: string;
  };

  type GenerateSuno = {
    /** 歌词 (自定义模式专用) */
    prompt?: string;
    /** mv模型，chirp-v3-0、chirp-v3-5。不写默认 chirp-v3-0 */
    mv?: string;
    /** 标题(自定义模式专用) */
    title?: string;
    /** 风格标签(自定义模式专用) */
    tags?: string;
    /** 是否生成纯音乐，true 为生成纯音乐 */
    make_instrumental?: boolean;
    /** 任务id，用于对之前的任务再操作 */
    task_id?: string;
    /** float，歌曲延长时间，单位秒 */
    continue_at?: number;
    /** 歌曲id，需要续写哪首歌 */
    continue_clip_id?: string;
    /** 灵感模式提示词(灵感模式专用) */
    gpt_description_prompt?: string;
    /** 回调地址 */
    notify_hook?: string;
  };

  type genListParams = {
    genTable: GenTable;
    pageQuery: PageQuery;
  };

  type GenTable = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 编号 */
    tableId?: number;
    /** 表名称 */
    tableName: string;
    /** 表描述 */
    tableComment: string;
    /** 关联父表的表名 */
    subTableName?: string;
    /** 本表关联父表的外键名 */
    subTableFkName?: string;
    /** 实体类名称(首字母大写) */
    className: string;
    /** 使用的模板（crud单表操作 tree树表操作 sub主子表操作） */
    tplCategory?: string;
    /** 生成包路径 */
    packageName: string;
    /** 生成模块名 */
    moduleName: string;
    /** 生成业务名 */
    businessName: string;
    /** 生成功能名 */
    functionName: string;
    /** 生成作者 */
    functionAuthor: string;
    /** 生成代码方式（0zip压缩包 1自定义路径） */
    genType?: string;
    /** 生成路径（不填默认项目路径） */
    genPath?: string;
    /** 主键信息 */
    pkColumn?: GenTableColumn;
    /** 表列信息 */
    columns?: GenTableColumn[];
    /** 其它生成选项 */
    options?: string;
    /** 备注 */
    remark?: string;
    /** 树编码字段 */
    treeCode?: string;
    /** 树父编码字段 */
    treeParentCode?: string;
    /** 树名称字段 */
    treeName?: string;
    menuIds?: number[];
    /** 上级菜单ID字段 */
    parentMenuId?: string;
    /** 上级菜单名称字段 */
    parentMenuName?: string;
    tree?: boolean;
    crud?: boolean;
  };

  type GenTableColumn = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 编号 */
    columnId?: number;
    /** 归属表编号 */
    tableId?: number;
    /** 列名称 */
    columnName?: string;
    /** 列描述 */
    columnComment?: string;
    /** 列类型 */
    columnType?: string;
    /** JAVA类型 */
    javaType?: string;
    /** JAVA字段名 */
    javaField: string;
    /** 是否主键（1是） */
    isPk?: string;
    /** 是否自增（1是） */
    isIncrement?: string;
    /** 是否必填（1是） */
    isRequired?: string;
    /** 是否为插入字段（1是） */
    isInsert?: string;
    /** 是否编辑字段（1是） */
    isEdit?: string;
    /** 是否列表字段（1是） */
    isList?: string;
    /** 是否查询字段（1是） */
    isQuery?: string;
    /** 查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围） */
    queryType?: string;
    /** 显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件） */
    htmlType?: string;
    /** 字典类型 */
    dictType?: string;
    /** 排序 */
    sort?: number;
    capJavaField?: string;
    list?: boolean;
    pk?: boolean;
    edit?: boolean;
    required?: boolean;
    insert?: boolean;
    usableColumn?: boolean;
    superColumn?: boolean;
    increment?: boolean;
    query?: boolean;
  };

  type getAttachInfoParams = {
    /** 主键 */
    id: number;
  };

  type getClausesByPactIdParams = {
    /** 清单ID */
    pactId: number;
  };

  type getConfigKey1Params = {
    /** 参数Key */
    configKey: string;
  };

  type getConfigKeyParams = {
    /** 参数Key */
    configKey: string;
  };

  type getGenerationTaskParams = {
    taskId: string;
  };

  type getInfo10Params = {
    /** 公告ID */
    noticeId: number;
  };

  type getInfo11Params = {
    /** 主键 */
    id: number;
  };

  type getInfo12Params = {
    /** 主键 */
    id: number;
  };

  type getInfo13Params = {
    /** 菜单ID */
    menuId: number;
  };

  type getInfo14Params = {
    /** 字典ID */
    dictId: number;
  };

  type getInfo15Params = {
    /** 字典code */
    dictCode: number;
  };

  type getInfo16Params = {
    /** 部门ID */
    deptId: number;
  };

  type getInfo17Params = {
    /** 参数ID */
    configId: number;
  };

  type getInfo18Params = {
    /** OSS配置ID */
    ossConfigId: number;
  };

  type getInfo20Params = {
    /** 主键 */
    id: number;
  };

  type getInfo3Params = {
    /** 用户ID */
    userId: number;
  };

  type getInfo4Params = {
    /** 主键 */
    packageId: number;
  };

  type getInfo5Params = {
    /** 主键 */
    id: number;
  };

  type getInfo6Params = {
    /** 角色ID */
    roleId: number;
  };

  type getInfo7Params = {
    /** 岗位ID */
    postId: number;
  };

  type getInfo8Params = {
    /** 主键 */
    id: number;
  };

  type getInfo9Params = {
    /** 主键 */
    id: number;
  };

  type getInfoParams = {
    /** 表ID */
    tableId: number;
  };

  type getNoticeParams = {
    notice: SysNoticeBo;
  };

  type getSeedParams = {
    id: string;
  };

  type importTableSaveParams = {
    /** 表名串 */
    tables: string;
  };

  type insertAuthRoleParams = {
    /** 用户Id */
    userId: number;
    /** 角色ID串 */
    roleIds: number[];
  };

  type InsightFace = {
    /** 本人头像json */
    sourceBase64?: string;
    /** 明星头像json */
    targetBase64?: string;
  };

  type KnowledgeAttachBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    id: number;
    /** 知识库ID */
    kid: string;
    /** 文档ID */
    docId: string;
    /** 文档名称 */
    docName: string;
    /** 文档类型 */
    docType: string;
    /** 文档内容 */
    content: string;
    /** 备注 */
    remark: string;
    /** 对象存储主键 */
    ossId: number;
    /** 拆解图片状态10未开始，20进行中，30已完成 */
    picStatus: number;
    /** 分析图片状态10未开始，20进行中，30已完成 */
    picAnysStatus: number;
    /** 写入向量数据库状态10未开始，20进行中，30已完成 */
    vectorStatus: number;
  };

  type KnowledgeAttachVo = {
    id?: number;
    /** 知识库ID */
    kid?: string;
    /** 文档ID */
    docId?: string;
    /** 文档名称 */
    docName?: string;
    /** 文档类型 */
    docType?: string;
    /** 文档内容 */
    content?: string;
    /** 备注 */
    remark?: string;
    /** 对象存储主键 */
    ossId?: number;
    /** 拆解图片状态10未开始，20进行中，30已完成 */
    picStatus?: number;
    /** 分析图片状态10未开始，20进行中，30已完成 */
    picAnysStatus?: number;
    /** 写入向量数据库状态10未开始，20进行中，30已完成 */
    vectorStatus?: number;
  };

  type KnowledgeFragmentBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    id: number;
    /** 知识库ID */
    kid: string;
    /** 文档ID */
    docId: string;
    /** 知识片段ID */
    fid: string;
    /** 片段索引下标 */
    idx: number;
    /** 文档内容 */
    content: string;
    /** 备注 */
    remark: string;
  };

  type KnowledgeFragmentVo = {
    id?: number;
    /** 知识库ID */
    kid?: string;
    /** 文档ID */
    docId?: string;
    /** 知识片段ID */
    fid?: string;
    /** 片段索引下标 */
    idx?: number;
    /** 文档内容 */
    content?: string;
    /** 备注 */
    remark?: string;
  };

  type KnowledgeInfoBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主键 */
    id: number;
    /** 知识库ID */
    kid: string;
    /** 用户ID */
    uid: number;
    /** 知识库名称 */
    kname: string;
    /** 是否公开知识库（0 否 1是） */
    share: number;
    /** 描述 */
    description?: string;
    /** 知识分隔符 */
    knowledgeSeparator?: string;
    /** 提问分隔符 */
    questionSeparator?: string;
    /** 重叠字符数 */
    overlapChar?: number;
    /** 知识库中检索的条数 */
    retrieveLimit: number;
    /** 文本块大小 */
    textBlockSize: number;
    /** 向量库模型名称 */
    vectorModelName: string;
    /** 向量化模型名称 */
    embeddingModelName: string;
    /** 系统提示词 */
    systemPrompt?: string;
    /** 备注 */
    remark?: string;
  };

  type KnowledgeInfoUploadBo = {
    kid?: string;
    file?: string;
  };

  type KnowledgeInfoVo = {
    id?: number;
    /** 知识库ID */
    kid?: string;
    /** 用户ID */
    uid?: number;
    /** 知识库名称 */
    kname?: string;
    /** 是否公开知识库（0 否 1是） */
    share?: number;
    /** 描述 */
    description?: string;
    /** 知识分隔符 */
    knowledgeSeparator?: string;
    /** 提问分隔符 */
    questionSeparator?: string;
    /** 重叠字符数 */
    overlapChar?: number;
    /** 知识库中检索的条数 */
    retrieveLimit?: number;
    /** 文本块大小 */
    textBlockSize?: number;
    /** 向量库模型名称 */
    vectorModelName?: string;
    /** 向量化模型名称 */
    embeddingModelName?: string;
    /** 系统提示词 */
    systemPrompt?: string;
    /** 备注 */
    remark?: string;
  };

  type list10Params = {
    menu: SysMenuBo;
  };

  type list11Params = {
    dictType: SysDictTypeBo;
    pageQuery: PageQuery;
  };

  type list12Params = {
    dictData: SysDictDataBo;
    pageQuery: PageQuery;
  };

  type list13Params = {
    dept: SysDeptBo;
  };

  type list14Params = {
    config: SysConfigBo;
    pageQuery: PageQuery;
  };

  type list15Params = {
    bo: SysOssBo;
    pageQuery: PageQuery;
  };

  type list16Params = {
    bo: SysOssConfigBo;
    pageQuery: PageQuery;
  };

  type list17Params = {
    operLog: SysOperLogBo;
    pageQuery: PageQuery;
  };

  type list18Params = {
    /** IP地址 */
    ipaddr: string;
    /** 用户名 */
    userName: string;
  };

  type list19Params = {
    logininfor: SysLogininforBo;
    pageQuery: PageQuery;
  };

  type list1Params = {
    bo: SysTenantPackageBo;
    pageQuery: PageQuery;
  };

  type list20Params = {
    bo: KnowledgeInfoBo;
    pageQuery: PageQuery;
  };

  type list21Params = {
    bo: ChatConfigBo;
    pageQuery: PageQuery;
  };

  type list2Params = {
    bo: ChatSessionBo;
    pageQuery: PageQuery;
  };

  type list3Params = {
    role: SysRoleBo;
    pageQuery: PageQuery;
  };

  type list4Params = {
    post: SysPostBo;
    pageQuery: PageQuery;
  };

  type list5Params = {
    bo: ChatPayOrderBo;
    pageQuery: PageQuery;
  };

  type list6Params = {
    bo: SysNoticeStateBo;
    pageQuery: PageQuery;
  };

  type list7Params = {
    notice: SysNoticeBo;
    pageQuery: PageQuery;
  };

  type list8Params = {
    bo: ChatModelBo;
    pageQuery: PageQuery;
  };

  type list9Params = {
    bo: ChatMessageBo;
    pageQuery: PageQuery;
  };

  type listByIds1Params = {
    /** OSS对象ID串 */
    ossIds: number[];
  };

  type listParams = {
    user: SysUserBo;
    pageQuery: PageQuery;
  };

  type LoginBody = {
    /** 租户ID */
    tenantId?: string;
    /** 用户名 */
    username: string;
    /** 用户密码 */
    password: string;
    /** 验证码 */
    code?: string;
    /** 唯一标识 */
    uuid?: string;
  };

  type LoginTenantVo = {
    /** 租户开关 */
    tenantEnabled?: boolean;
    /** 租户对象列表 */
    voList?: TenantListVo[];
  };

  type LoginUser = {
    /** 租户ID */
    tenantId?: string;
    /** 用户ID */
    userId?: number;
    /** 部门ID */
    deptId?: number;
    /** 部门名 */
    deptName?: string;
    /** 用户唯一标识 */
    token?: string;
    /** 用户类型 */
    userType?: string;
    /** 登录时间 */
    loginTime?: number;
    /** 过期时间 */
    expireTime?: number;
    /** 登录IP地址 */
    ipaddr?: string;
    /** 登录地点 */
    loginLocation?: string;
    /** 浏览器类型 */
    browser?: string;
    /** 操作系统 */
    os?: string;
    /** 菜单权限 */
    menuPermission?: string[];
    /** 角色权限 */
    rolePermission?: string[];
    /** 用户名 */
    username?: string;
    /** 用户名 */
    nickName?: string;
    /** 微信头像 */
    avatar?: string;
    /** 角色对象 */
    roles?: RoleDTO[];
    /** 数据权限 当前角色ID */
    roleId?: number;
    /** 获取登录id */
    loginId?: string;
  };

  type LoginVo = {
    token?: string;
    access_token?: string;
    userInfo?: LoginUser;
  };

  type lyricsParams = {
    taskId: string;
  };

  type MenuTreeSelectVo = {
    /** 选中菜单列表 */
    checkedKeys?: number[];
    /** 菜单下拉树结构列表 */
    menus?: TreeLong[];
  };

  type Message = {
    /** 目前支持四个中角色参考官网，进行情景输入：
 https://platform.openai.com/docs/guides/chat/introduction */
    role?: string;
    name?: string;
    content?: Record<string, any>;
    /** The tool calls generated by the model, such as function calls. */
    tool_calls?: ToolCalls[];
    tool_call_id?: string;
    function_call?: FunctionCall;
    reasoning_content?: string;
  };

  type MetaVo = {
    /** 设置该路由在侧边栏和面包屑中展示的名字 */
    title?: string;
    /** 设置该路由的图标，对应路径src/assets/icons/svg */
    icon?: string;
    /** 设置为true，则不会被 <keep-alive>缓存 */
    noCache?: boolean;
    /** 内链地址（http(s)://开头） */
    link?: string;
  };

  type modelListParams = {
    bo: ChatModelBo;
  };

  type PactBo = {
    /** minio文件上传后传回的url */
    minioFileUrl?: string;
    /** 合同清单id */
    pactId?: number;
    /** 审查立场汉语名称 */
    dictLabel?: string;
  };

  type PactListBo = {
    /** 主键，新增非必填，修改必填 */
    pactId: number;
    /** 清单名称 */
    listName: string;
    /** 清单描述 */
    listDescription?: string;
    /** 清单来源：0：默认清单，1:用户创建 */
    listSource: string;
    /** 清单创建方式：0：从已有清单创建，1:从空白清单创建 */
    listCreate: string;
    /** 清单创建来源Id */
    listCreateId?: number;
    /** 字典键值 */
    dictValue?: string;
    /** 默认审查立场名称(字典标签) */
    dictLabel?: string;
    /** 清单关联审查立场Value(字典键值)集合 */
    dictValueList?: string[];
  };

  type PactListClauseBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 合同清单条款主键，新增非必填，修改必填 */
    clauseId: number;
    /** 合同清单id */
    pactId: number;
    /** 条款名称 */
    clauseName: string;
    /** 条款说明 */
    clauseDescription: string;
    /** 条款下风险点 */
    pactListClauseRiskList?: PactListClauseRiskBo[];
  };

  type PactListClauseRiskBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主键，新增非必填，修改必填 */
    riskId: number;
    /** 合同清单条款表ID，新增不能为空，修改可以 */
    clauseId: number;
    /** 风险点名称 */
    riskName: string;
    /** 风险点说明 */
    riskDescription?: string;
    /** 清单ID */
    pactId?: number;
    /** 启用或停用；0:启用，1：停用 */
    riskStatus: string;
  };

  type PactListClauseRiskVo = {
    /** 风险点主键，新增非必填，修改必填 */
    riskId?: number;
    /** 合同清单条款表ID，新增不能为空，修改可以 */
    clauseId?: number;
    /** 风险点名称 */
    riskName?: string;
    /** 风险点说明 */
    riskDescription?: string;
    /** 清单ID */
    pactId?: number;
    /** 启用或停用；0:启用，1：停用 */
    riskStatus?: string;
  };

  type PactListClauseVo = {
    /** 合同清单条款 */
    clauseId?: number;
    /** 合同清单id */
    pactId?: number;
    /** 条款名称 */
    clauseName?: string;
    /** 条款说明 */
    clauseDescription?: string;
    /** 条款下风险点 */
    pactListClauseRiskList?: PactListClauseRiskVo[];
  };

  type PactListStanceVo = {
    /** 合同清单ID主键 */
    pactId?: number;
    /** 字典标签 */
    dictLabel?: string;
    /** 审查立场键值(字典键值) */
    dictValue?: string;
  };

  type PactListVo = {
    /** 主键 */
    pactId?: number;
    /** 清单名称 */
    listName?: string;
    /** 清单描述 */
    listDescription?: string;
    /** 清单来源：0：默认清单，1:用户创建 */
    listSource?: string;
    /** 清单创建方式：0：从已有清单创建，1:从空白清单创建 */
    listCreate?: string;
    /** 清单创建来源Id */
    listCreateId?: number;
    /** 审查立场键值(字典键值) */
    dictValue?: string;
    /** 审查立场名称(字典标签) */
    dictLabel?: string;
    /** 清单是否发布；0:未发布，1：已发布 */
    listRelease?: string;
    /** 清单是否删除；0:未删除，1：已删除 */
    listDelete?: string;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 当前清单绑定的审查立场集合 */
    pactListStanceList?: PactListStanceVo[];
    /** 用户合同清单 */
    userPactList?: PactListVo[];
    /** 系统预置合同清单 */
    sysPactList?: PactListVo[];
  };

  type PageQuery = {
    /** 分页大小 */
    pageSize?: number;
    /** 当前页数 */
    pageNum?: number;
    /** 排序列 */
    orderByColumn?: string;
    /** 排序的方向desc或者asc */
    isAsc?: string;
  };

  type previewParams = {
    /** 表ID */
    tableId: number;
  };

  type ProfileVo = {
    /** 用户信息 */
    user?: SysUserVo;
    /** 用户所属角色组 */
    roleGroup?: string;
    /** 用户所属岗位组 */
    postGroup?: string;
  };

  type RAvatarVo = {
    code?: number;
    msg?: string;
    data?: AvatarVo;
  };

  type RCacheListInfoVo = {
    code?: number;
    msg?: string;
    data?: CacheListInfoVo;
  };

  type RCaptchaVo = {
    code?: number;
    msg?: string;
    data?: CaptchaVo;
  };

  type RChatConfigVo = {
    code?: number;
    msg?: string;
    data?: ChatConfigVo;
  };

  type RChatMessageVo = {
    code?: number;
    msg?: string;
    data?: ChatMessageVo;
  };

  type RChatModel = {
    code?: number;
    msg?: string;
    data?: ChatModel;
  };

  type RChatModelVo = {
    code?: number;
    msg?: string;
    data?: ChatModelVo;
  };

  type RChatPayOrderVo = {
    code?: number;
    msg?: string;
    data?: ChatPayOrderVo;
  };

  type RChatSessionVo = {
    code?: number;
    msg?: string;
    data?: ChatSessionVo;
  };

  type RDeptTreeSelectVo = {
    code?: number;
    msg?: string;
    data?: DeptTreeSelectVo;
  };

  type RegisterBody = {
    /** 租户ID */
    tenantId?: string;
    /** 用户名 */
    username: string;
    /** 用户密码 */
    password: string;
    /** 验证码 */
    code?: string;
    /** 唯一标识 */
    uuid?: string;
    userType?: string;
    /** 注册域名 */
    domainName?: string;
  };

  type remove10Params = {
    /** 主键串 */
    ids: number[];
  };

  type remove11Params = {
    /** 公告ID串 */
    noticeIds: number[];
  };

  type remove12Params = {
    /** 主键串 */
    ids: number[];
  };

  type remove13Params = {
    /** 主键串 */
    ids: number[];
  };

  type remove14Params = {
    /** 字典ID串 */
    dictIds: number[];
  };

  type remove15Params = {
    /** 字典code串 */
    dictCodes: number[];
  };

  type remove16Params = {
    /** 参数ID串 */
    configIds: number[];
  };

  type remove17Params = {
    /** OSS对象ID串 */
    ossIds: number[];
  };

  type remove18Params = {
    /** OSS配置ID串 */
    ossConfigIds: number[];
  };

  type remove1Params = {
    /** 菜单ID */
    menuId: number;
  };

  type remove20Params = {
    /** 清单ID */
    pactIds: number[];
  };

  type remove21Params = {
    /** 日志ids */
    operIds: number[];
  };

  type remove22Params = {
    /** 日志ids */
    infoIds: number[];
  };

  type remove23Params = {
    /** 主键串 */
    ids: number[];
  };

  type remove2Params = {
    /** 部门ID */
    deptId: number;
  };

  type remove3Params = {
    /** 表ID串 */
    tableIds: number[];
  };

  type remove4Params = {
    /** 角色ID串 */
    userIds: number[];
  };

  type remove5Params = {
    /** 主键串 */
    packageIds: number[];
  };

  type remove6Params = {
    /** 主键串 */
    ids: number[];
  };

  type remove7Params = {
    /** 角色ID串 */
    roleIds: number[];
  };

  type remove8Params = {
    /** 岗位ID串 */
    postIds: number[];
  };

  type remove9Params = {
    /** 主键串 */
    ids: number[];
  };

  type removeAttachParams = {
    kid: string;
  };

  type removeParams = {
    id: string;
  };

  type RKnowledgeAttachVo = {
    code?: number;
    msg?: string;
    data?: KnowledgeAttachVo;
  };

  type RList = {
    code?: number;
    msg?: string;
    data?: Record<string, any>[];
  };

  type RListChatConfigVo = {
    code?: number;
    msg?: string;
    data?: ChatConfigVo[];
  };

  type RListChatModelVo = {
    code?: number;
    msg?: string;
    data?: ChatModelVo[];
  };

  type RListPactListClauseVo = {
    code?: number;
    msg?: string;
    data?: PactListClauseVo[];
  };

  type RListRouterVo = {
    code?: number;
    msg?: string;
    data?: RouterVo[];
  };

  type RListSysDeptVo = {
    code?: number;
    msg?: string;
    data?: SysDeptVo[];
  };

  type RListSysDictDataVo = {
    code?: number;
    msg?: string;
    data?: SysDictDataVo[];
  };

  type RListSysDictTypeVo = {
    code?: number;
    msg?: string;
    data?: SysDictTypeVo[];
  };

  type RListSysMenuVo = {
    code?: number;
    msg?: string;
    data?: SysMenuVo[];
  };

  type RListSysOssVo = {
    code?: number;
    msg?: string;
    data?: SysOssVo[];
  };

  type RListSysPostVo = {
    code?: number;
    msg?: string;
    data?: SysPostVo[];
  };

  type RListSysRoleVo = {
    code?: number;
    msg?: string;
    data?: SysRoleVo[];
  };

  type RListSysTenantPackageVo = {
    code?: number;
    msg?: string;
    data?: SysTenantPackageVo[];
  };

  type RListSysUserOptionVo = {
    code?: number;
    msg?: string;
    data?: SysUserOptionVo[];
  };

  type RLoginTenantVo = {
    code?: number;
    msg?: string;
    data?: LoginTenantVo;
  };

  type RLoginVo = {
    code?: number;
    msg?: string;
    data?: LoginVo;
  };

  type RLong = {
    code?: number;
    msg?: string;
    data?: number;
  };

  type RMapStringObject = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type RMapStringString = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type RMenuTreeSelectVo = {
    code?: number;
    msg?: string;
    data?: MenuTreeSelectVo;
  };

  type RObject = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type roleDeptTreeselectParams = {
    /** 角色ID */
    roleId: number;
  };

  type RoleDTO = {
    /** 角色ID */
    roleId?: number;
    /** 角色名称 */
    roleName?: string;
    /** 角色权限 */
    roleKey?: string;
    /** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
    dataScope?: string;
  };

  type roleMenuTreeselectParams = {
    /** 角色ID */
    roleId: number;
  };

  type rolemModelListSelectParams = {
    /** 角色ID */
    roleId: number;
  };

  type RouterVo = {
    /** 路由名字 */
    name?: string;
    /** 路由地址 */
    path?: string;
    /** 是否隐藏路由，当设置 true 的时候该路由不会再侧边栏出现 */
    hidden?: boolean;
    /** 重定向地址，当设置 noRedirect 的时候该路由在面包屑导航中不可被点击 */
    redirect?: string;
    /** 组件地址 */
    component?: string;
    /** 路由参数：如 {"id": 1, "name": "ry"} */
    query?: string;
    /** 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面 */
    alwaysShow?: boolean;
    /** 其他元素 */
    meta?: MetaVo;
  };

  type RPactListVo = {
    code?: number;
    msg?: string;
    data?: PactListVo;
  };

  type RProfileVo = {
    code?: number;
    msg?: string;
    data?: ProfileVo;
  };

  type RString = {
    code?: number;
    msg?: string;
    data?: string;
  };

  type RSysConfigVo = {
    code?: number;
    msg?: string;
    data?: SysConfigVo;
  };

  type RSysDeptVo = {
    code?: number;
    msg?: string;
    data?: SysDeptVo;
  };

  type RSysDictDataVo = {
    code?: number;
    msg?: string;
    data?: SysDictDataVo;
  };

  type RSysDictTypeVo = {
    code?: number;
    msg?: string;
    data?: SysDictTypeVo;
  };

  type RSysMenuVo = {
    code?: number;
    msg?: string;
    data?: SysMenuVo;
  };

  type RSysNotice = {
    code?: number;
    msg?: string;
    data?: SysNotice;
  };

  type RSysNoticeStateVo = {
    code?: number;
    msg?: string;
    data?: SysNoticeStateVo;
  };

  type RSysNoticeVo = {
    code?: number;
    msg?: string;
    data?: SysNoticeVo;
  };

  type RSysOssConfigVo = {
    code?: number;
    msg?: string;
    data?: SysOssConfigVo;
  };

  type RSysOssUploadVo = {
    code?: number;
    msg?: string;
    data?: SysOssUploadVo;
  };

  type RSysPostVo = {
    code?: number;
    msg?: string;
    data?: SysPostVo;
  };

  type RSysRoleChatModelVo = {
    code?: number;
    msg?: string;
    data?: SysRoleChatModelVo;
  };

  type RSysRoleVo = {
    code?: number;
    msg?: string;
    data?: SysRoleVo;
  };

  type RSysTenantPackageVo = {
    code?: number;
    msg?: string;
    data?: SysTenantPackageVo;
  };

  type RSysUserInfoVo = {
    code?: number;
    msg?: string;
    data?: SysUserInfoVo;
  };

  type RSysUserVo = {
    code?: number;
    msg?: string;
    data?: SysUserVo;
  };

  type RUserInfoVo = {
    code?: number;
    msg?: string;
    data?: UserInfoVo;
  };

  type RVoid = {
    code?: number;
    msg?: string;
    data?: Record<string, any>;
  };

  type selectAuthUserAllParams = {
    /** 角色ID */
    roleId: number;
    /** 用户ID串 */
    userIds: number[];
  };

  type smsCodeParams = {
    /** 用户手机号 */
    phonenumber: string;
  };

  type SmsLoginBody = {
    /** 租户ID */
    tenantId: string;
    /** 手机号 */
    phonenumber: string;
    /** 短信code */
    smsCode: string;
  };

  type SseEmitter = {
    timeout?: number;
  };

  type SubmitActionDTO = {
    customId?: string;
    taskId?: string;
    state?: string;
    notifyHook?: string;
  };

  type SubmitBlendDTO = {
    state?: string;
    notifyHook?: string;
    base64Array?: string[];
    dimensions?: "PORTRAIT" | "SQUARE" | "LANDSCAPE";
  };

  type SubmitChangeDTO = {
    state?: string;
    notifyHook?: string;
    taskId?: string;
    action?:
      | "IMAGINE"
      | "UPSCALE"
      | "VARIATION"
      | "REROLL"
      | "DESCRIBE"
      | "BLEND";
    index?: number;
  };

  type SubmitDescribeDTO = {
    state?: string;
    notifyHook?: string;
    base64?: string;
  };

  type SubmitImagineDTO = {
    state?: string;
    notifyHook?: string;
    prompt?: string;
    base64Array?: string[];
    base64?: string;
  };

  type SubmitModalDTO = {
    state?: string;
    notifyHook?: string;
    maskBase64?: string;
    taskId?: string;
    prompt?: string;
  };

  type SubmitShortenDTO = {
    state?: string;
    notifyHook?: string;
    botType?: string;
    prompt?: string;
  };

  type SubmitSimpleChangeDTO = {
    state?: string;
    notifyHook?: string;
    content?: string;
  };

  type synchDbParams = {
    /** 表名 */
    tableName: string;
  };

  type SysConfigBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 参数主键 */
    configId: number;
    /** 参数名称 */
    configName: string;
    /** 参数键名 */
    configKey: string;
    /** 参数键值 */
    configValue: string;
    /** 系统内置（Y是 N否） */
    configType?: string;
    /** 备注 */
    remark?: string;
  };

  type SysConfigVo = {
    /** 参数主键 */
    configId?: number;
    /** 参数名称 */
    configName?: string;
    /** 参数键名 */
    configKey?: string;
    /** 参数键值 */
    configValue?: string;
    /** 系统内置（Y是 N否） */
    configType?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type SysDeptBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 部门id */
    deptId: number;
    /** 父部门ID */
    parentId?: number;
    /** 部门名称 */
    deptName: string;
    /** 显示顺序 */
    orderNum: number;
    /** 负责人 */
    leader?: string;
    /** 联系电话 */
    phone?: string;
    /** 邮箱 */
    email?: string;
    /** 部门状态（0正常 1停用） */
    status?: string;
  };

  type SysDeptVo = {
    /** 部门id */
    deptId?: number;
    /** 父部门id */
    parentId?: number;
    /** 父部门名称 */
    parentName?: string;
    /** 祖级列表 */
    ancestors?: string;
    /** 部门名称 */
    deptName?: string;
    /** 显示顺序 */
    orderNum?: number;
    /** 负责人 */
    leader?: string;
    /** 联系电话 */
    phone?: string;
    /** 邮箱 */
    email?: string;
    /** 部门状态（0正常 1停用） */
    status?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type SysDictDataBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 字典编码 */
    dictCode: number;
    /** 字典排序 */
    dictSort?: number;
    /** 字典标签 */
    dictLabel: string;
    /** 字典键值 */
    dictValue: string;
    /** 字典类型 */
    dictType: string;
    /** 样式属性（其他样式扩展） */
    cssClass?: string;
    /** 表格回显样式 */
    listClass?: string;
    /** 是否默认（Y是 N否） */
    isDefault?: string;
    /** 状态（0正常 1停用） */
    status?: string;
    /** 备注 */
    remark?: string;
  };

  type SysDictDataVo = {
    /** 字典编码 */
    dictCode?: number;
    /** 字典排序 */
    dictSort?: number;
    /** 字典标签 */
    dictLabel?: string;
    /** 字典键值 */
    dictValue?: string;
    /** 字典类型 */
    dictType?: string;
    /** 样式属性（其他样式扩展） */
    cssClass?: string;
    /** 表格回显样式 */
    listClass?: string;
    /** 是否默认（Y是 N否） */
    isDefault?: string;
    /** 状态（0正常 1停用） */
    status?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type SysDictTypeBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 字典主键 */
    dictId: number;
    /** 字典名称 */
    dictName: string;
    /** 字典类型 */
    dictType: string;
    /** 状态（0正常 1停用） */
    status?: string;
    /** 备注 */
    remark?: string;
  };

  type SysDictTypeVo = {
    /** 字典主键 */
    dictId?: number;
    /** 字典名称 */
    dictName?: string;
    /** 字典类型 */
    dictType?: string;
    /** 状态（0正常 1停用） */
    status?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type SysLogininforBo = {
    /** 访问ID */
    infoId?: number;
    /** 租户编号 */
    tenantId?: string;
    /** 用户账号 */
    userName?: string;
    /** 登录IP地址 */
    ipaddr?: string;
    /** 登录地点 */
    loginLocation?: string;
    /** 浏览器类型 */
    browser?: string;
    /** 操作系统 */
    os?: string;
    /** 登录状态（0成功 1失败） */
    status?: string;
    /** 提示消息 */
    msg?: string;
    /** 访问时间 */
    loginTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
  };

  type SysLogininforVo = {
    /** 访问ID */
    infoId?: number;
    /** 租户编号 */
    tenantId?: string;
    /** 用户账号 */
    userName?: string;
    /** 登录状态（0成功 1失败） */
    status?: string;
    /** 登录IP地址 */
    ipaddr?: string;
    /** 登录地点 */
    loginLocation?: string;
    /** 浏览器类型 */
    browser?: string;
    /** 操作系统 */
    os?: string;
    /** 提示消息 */
    msg?: string;
    /** 访问时间 */
    loginTime?: string;
  };

  type SysMenuBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 菜单ID */
    menuId: number;
    /** 父菜单ID */
    parentId?: number;
    /** 菜单名称 */
    menuName: string;
    /** 显示顺序 */
    orderNum: number;
    /** 路由地址 */
    path?: string;
    /** 组件路径 */
    component?: string;
    /** 路由参数 */
    queryParam?: string;
    /** 是否为外链（0是 1否） */
    isFrame?: string;
    /** 是否缓存（0缓存 1不缓存） */
    isCache?: string;
    /** 菜单类型（M目录 C菜单 F按钮） */
    menuType: string;
    /** 显示状态（0显示 1隐藏） */
    visible?: string;
    /** 菜单状态（0正常 1停用） */
    status?: string;
    /** 权限标识 */
    perms?: string;
    /** 菜单图标 */
    icon?: string;
    /** 备注 */
    remark?: string;
  };

  type SysMenuVo = {
    /** 菜单ID */
    menuId?: number;
    /** 菜单名称 */
    menuName?: string;
    /** 父菜单ID */
    parentId?: number;
    /** 显示顺序 */
    orderNum?: number;
    /** 路由地址 */
    path?: string;
    /** 组件路径 */
    component?: string;
    /** 路由参数 */
    queryParam?: string;
    /** 是否为外链（0是 1否） */
    isFrame?: string;
    /** 是否缓存（0缓存 1不缓存） */
    isCache?: string;
    /** 菜单类型（M目录 C菜单 F按钮） */
    menuType?: string;
    /** 显示状态（0显示 1隐藏） */
    visible?: string;
    /** 菜单状态（0正常 1停用） */
    status?: string;
    /** 权限标识 */
    perms?: string;
    /** 菜单图标 */
    icon?: string;
    /** 创建部门 */
    createDept?: number;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
    /** 子菜单 */
    children?: SysMenuVo[];
  };

  type SysNotice = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 租户编号 */
    tenantId?: string;
    /** 公告ID */
    noticeId?: number;
    /** 公告标题 */
    noticeTitle?: string;
    /** 公告类型（1通知 2公告） */
    noticeType?: string;
    /** 公告内容 */
    noticeContent?: string;
    /** 公告状态（0正常 1关闭） */
    status?: string;
    /** 备注 */
    remark?: string;
  };

  type SysNoticeBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 公告ID */
    noticeId: number;
    /** 公告标题 */
    noticeTitle: string;
    /** 公告类型（1通知 2公告） */
    noticeType?: string;
    /** 公告内容 */
    noticeContent?: string;
    /** 公告状态（0正常 1关闭） */
    status?: string;
    /** 备注 */
    remark?: string;
    /** 创建人名称 */
    createByName?: string;
  };

  type SysNoticeStateBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** ID */
    id: number;
    /** 用户ID */
    userId: number;
    /** 公告ID */
    noticeId: number;
    /** 阅读状态（0未读 1已读） */
    readStatus: string;
    /** 备注 */
    remark: string;
  };

  type SysNoticeStateVo = {
    /** ID */
    id?: number;
    /** 用户ID */
    userId?: number;
    /** 公告ID */
    noticeId?: number;
    /** 阅读状态（0未读 1已读） */
    readStatus?: string;
    /** 备注 */
    remark?: string;
  };

  type SysNoticeVo = {
    /** 公告ID */
    noticeId?: number;
    /** 公告标题 */
    noticeTitle?: string;
    /** 公告类型（1通知 2公告） */
    noticeType?: string;
    /** 公告内容 */
    noticeContent?: string;
    /** 公告状态（0正常 1关闭） */
    status?: string;
    /** 备注 */
    remark?: string;
  };

  type SysOperLogBo = {
    /** 日志主键 */
    operId?: number;
    /** 租户编号 */
    tenantId?: string;
    /** 模块标题 */
    title?: string;
    /** 业务类型（0其它 1新增 2修改 3删除） */
    businessType?: number;
    /** 业务类型数组 */
    businessTypes?: number[];
    /** 方法名称 */
    method?: string;
    /** 请求方式 */
    requestMethod?: string;
    /** 操作类别（0其它 1后台用户 2手机端用户） */
    operatorType?: number;
    /** 操作人员 */
    operName?: string;
    /** 部门名称 */
    deptName?: string;
    /** 请求URL */
    operUrl?: string;
    /** 主机地址 */
    operIp?: string;
    /** 操作地点 */
    operLocation?: string;
    /** 请求参数 */
    operParam?: string;
    /** 返回参数 */
    jsonResult?: string;
    /** 操作状态（0正常 1异常） */
    status?: number;
    /** 错误消息 */
    errorMsg?: string;
    /** 操作时间 */
    operTime?: string;
    /** 消耗时间 */
    costTime?: number;
    /** 请求参数 */
    params?: Record<string, any>;
  };

  type SysOperLogVo = {
    /** 日志主键 */
    operId?: number;
    /** 租户编号 */
    tenantId?: string;
    /** 模块标题 */
    title?: string;
    /** 业务类型（0其它 1新增 2修改 3删除） */
    businessType?: number;
    /** 业务类型数组 */
    businessTypes?: number[];
    /** 方法名称 */
    method?: string;
    /** 请求方式 */
    requestMethod?: string;
    /** 操作类别（0其它 1后台用户 2手机端用户） */
    operatorType?: number;
    /** 操作人员 */
    operName?: string;
    /** 部门名称 */
    deptName?: string;
    /** 请求URL */
    operUrl?: string;
    /** 主机地址 */
    operIp?: string;
    /** 操作地点 */
    operLocation?: string;
    /** 请求参数 */
    operParam?: string;
    /** 返回参数 */
    jsonResult?: string;
    /** 操作状态（0正常 1异常） */
    status?: number;
    /** 错误消息 */
    errorMsg?: string;
    /** 操作时间 */
    operTime?: string;
    /** 消耗时间 */
    costTime?: number;
  };

  type SysOssBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** ossId */
    ossId?: number;
    /** 文件名 */
    fileName?: string;
    /** 原名 */
    originalName?: string;
    /** 文件后缀名 */
    fileSuffix?: string;
    /** URL地址 */
    url?: string;
    /** 服务商 */
    service?: string;
  };

  type SysOssConfigBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 主建 */
    ossConfigId: number;
    /** 配置key */
    configKey: string;
    /** accessKey */
    accessKey: string;
    /** 秘钥 */
    secretKey: string;
    /** 桶名称 */
    bucketName: string;
    /** 前缀 */
    prefix?: string;
    /** 访问站点 */
    endpoint: string;
    /** 自定义域名 */
    domain?: string;
    /** 是否https（Y=是,N=否） */
    isHttps?: string;
    /** 是否默认（0=是,1=否） */
    status?: string;
    /** 域 */
    region?: string;
    /** 扩展字段 */
    ext1?: string;
    /** 备注 */
    remark?: string;
    /** 桶权限类型(0private 1public 2custom) */
    accessPolicy: string;
  };

  type SysOssConfigVo = {
    /** 主建 */
    ossConfigId?: number;
    /** 配置key */
    configKey?: string;
    /** accessKey */
    accessKey?: string;
    /** 秘钥 */
    secretKey?: string;
    /** 桶名称 */
    bucketName?: string;
    /** 前缀 */
    prefix?: string;
    /** 访问站点 */
    endpoint?: string;
    /** 自定义域名 */
    domain?: string;
    /** 是否https（Y=是,N=否） */
    isHttps?: string;
    /** 域 */
    region?: string;
    /** 是否默认（0=是,1=否） */
    status?: string;
    /** 扩展字段 */
    ext1?: string;
    /** 备注 */
    remark?: string;
    /** 桶权限类型(0private 1public 2custom) */
    accessPolicy?: string;
  };

  type SysOssUploadVo = {
    /** URL地址 */
    url?: string;
    /** 文件名 */
    fileName?: string;
    /** 对象存储主键 */
    ossId?: string;
  };

  type SysOssVo = {
    /** 对象存储主键 */
    ossId?: number;
    /** 文件名 */
    fileName?: string;
    /** 原名 */
    originalName?: string;
    /** 文件后缀名 */
    fileSuffix?: string;
    /** URL地址 */
    url?: string;
    /** 创建时间 */
    createTime?: string;
    /** 上传人 */
    createBy?: number;
    /** 上传人名称 */
    createByName?: string;
    /** 服务商 */
    service?: string;
  };

  type SysPostBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 岗位ID */
    postId: number;
    /** 岗位编码 */
    postCode: string;
    /** 岗位名称 */
    postName: string;
    /** 显示顺序 */
    postSort: number;
    /** 所属部门 */
    deptId: number;
    /** 状态（0正常 1停用） */
    status?: string;
    /** 备注 */
    remark?: string;
  };

  type SysPostVo = {
    /** 岗位ID */
    postId?: number;
    /** 岗位编码 */
    postCode?: string;
    /** 岗位名称 */
    postName?: string;
    /** 显示顺序 */
    postSort?: number;
    /** 所属部门 */
    deptId?: number;
    /** 状态（0正常 1停用） */
    status?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type SysRoleBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 角色ID */
    roleId: number;
    /** 角色名称 */
    roleName: string;
    /** 角色权限字符串 */
    roleKey: string;
    /** 显示顺序 */
    roleSort: number;
    /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） */
    dataScope?: string;
    /** 菜单树选择项是否关联显示 */
    menuCheckStrictly?: boolean;
    /** 部门树选择项是否关联显示 */
    deptCheckStrictly?: boolean;
    /** 角色状态（0正常 1停用） */
    status?: string;
    /** 备注 */
    remark?: string;
    /** 菜单组 */
    menuIds?: number[];
    /** 部门组（数据权限） */
    deptIds?: number[];
    superAdmin?: boolean;
  };

  type SysRoleChatModelBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 角色ID */
    roleId: number;
    /** 模型ID组 */
    chatModelIds?: number[];
  };

  type SysRoleChatModelVo = {
    /** 选中的模型列表 */
    checkedKeys?: number[];
    /** 模型列表 */
    chatModels?: ChatModelVo[];
  };

  type SysRoleVo = {
    /** 角色ID */
    roleId?: number;
    /** 角色名称 */
    roleName?: string;
    /** 角色权限字符串 */
    roleKey?: string;
    /** 显示顺序 */
    roleSort?: number;
    /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） */
    dataScope?: string;
    /** 菜单树选择项是否关联显示 */
    menuCheckStrictly?: boolean;
    /** 部门树选择项是否关联显示 */
    deptCheckStrictly?: boolean;
    /** 角色状态（0正常 1停用） */
    status?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
    /** 用户是否存在此角色标识 默认不存在 */
    flag?: boolean;
    superAdmin?: boolean;
  };

  type SysTenantPackageBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 租户套餐id */
    packageId: number;
    /** 套餐名称 */
    packageName: string;
    /** 关联菜单id */
    menuIds?: number[];
    /** 备注 */
    remark?: string;
    /** 菜单树选择项是否关联显示 */
    menuCheckStrictly?: boolean;
    /** 状态（0正常 1停用） */
    status?: string;
  };

  type SysTenantPackageVo = {
    /** 租户套餐id */
    packageId?: number;
    /** 套餐名称 */
    packageName?: string;
    /** 关联菜单id */
    menuIds?: string;
    /** 备注 */
    remark?: string;
    /** 菜单树选择项是否关联显示 */
    menuCheckStrictly?: boolean;
    /** 状态（0正常 1停用） */
    status?: string;
  };

  type SysUserBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 用户ID */
    userId?: number;
    /** 部门ID */
    deptId?: number;
    /** 用户账号 */
    userName: string;
    /** 用户昵称 */
    nickName?: string;
    /** 用户类型（sys_user系统用户） */
    userType?: string;
    /** 用户邮箱 */
    email?: string;
    /** 手机号码 */
    phonenumber?: string;
    /** 用户性别（0男 1女 2未知） */
    sex?: string;
    /** 密码 */
    password?: string;
    /** 用户套餐 */
    userPlan?: string;
    /** 帐号状态（0正常 1停用） */
    status?: string;
    /** 微信头像 */
    avatar?: string;
    /** 备注 */
    remark?: string;
    /** 注册域名 */
    domainName?: string;
    /** 角色组 */
    roleIds?: number[];
    /** 岗位组 */
    postIds?: number[];
    /** 数据权限 当前角色ID */
    roleId?: number;
    /** 普通用户的标识,对当前开发者帐号唯一。一个openid对应一个公众号或小程序 */
    openId?: string;
    /** 用户等级 */
    userGrade?: string;
    /** 用户余额 */
    userBalance?: number;
    superAdmin?: boolean;
  };

  type SysUserInfoVo = {
    /** 用户信息 */
    user?: SysUserVo;
    /** 角色ID列表 */
    roleIds?: number[];
    /** 角色列表 */
    roles?: SysRoleVo[];
    /** 岗位ID列表 */
    postIds?: number[];
    /** 岗位列表 */
    posts?: SysPostVo[];
  };

  type SysUserOnline = {
    /** 会话编号 */
    tokenId?: string;
    /** 部门名称 */
    deptName?: string;
    /** 用户名称 */
    userName?: string;
    /** 登录IP地址 */
    ipaddr?: string;
    /** 登录地址 */
    loginLocation?: string;
    /** 浏览器类型 */
    browser?: string;
    /** 操作系统 */
    os?: string;
    /** 登录时间 */
    loginTime?: number;
  };

  type SysUserOptionVo = {
    /** 用户ID */
    userId?: number;
    /** 用户账号 */
    name?: string;
  };

  type SysUserPasswordBo = {
    /** 旧密码 */
    oldPassword: string;
    /** 新密码 */
    newPassword: string;
  };

  type SysUserProfileBo = {
    /** 创建部门 */
    createDept?: number;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 请求参数 */
    params?: Record<string, any>;
    /** 用户ID */
    userId?: number;
    /** 用户昵称 */
    nickName?: string;
    /** 用户邮箱 */
    email?: string;
    /** 手机号码 */
    phonenumber?: string;
    /** 用户性别（0男 1女 2未知） */
    sex?: string;
  };

  type SysUserRole = {
    /** 用户ID */
    userId?: number;
    /** 角色ID */
    roleId?: number;
  };

  type SysUserVo = {
    /** 用户ID */
    userId?: number;
    /** 租户ID */
    tenantId?: string;
    /** 部门ID */
    deptId?: number;
    /** 用户账号 */
    userName?: string;
    /** 用户套餐 */
    userPlan?: string;
    /** 用户昵称 */
    nickName?: string;
    /** 用户类型（sys_user系统用户） */
    userType?: string;
    /** 用户邮箱 */
    email?: string;
    /** 手机号码 */
    phonenumber?: string;
    /** 用户性别（0男 1女 2未知） */
    sex?: string;
    /** 头像地址 */
    avatar?: string;
    /** 微信头像地址 */
    wxAvatar?: string;
    /** 帐号状态（0正常 1停用） */
    status?: string;
    /** 最后登录IP */
    loginIp?: string;
    /** 最后登录时间 */
    loginDate?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: string;
    /** 部门对象 */
    dept?: SysDeptVo;
    /** 注册域名 */
    domainName?: string;
    /** 角色对象 */
    roles?: SysRoleVo[];
    /** 角色组 */
    roleIds?: number[];
    /** 岗位组 */
    postIds?: number[];
    /** 数据权限 当前角色ID */
    roleId?: number;
    /** 用户等级 */
    userGrade?: string;
    /** 用户余额 */
    userBalance?: number;
  };

  type TableDataInfoChatConfigVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: ChatConfigVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoChatMessageVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: ChatMessageVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoChatModelVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: ChatModelVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoChatPayOrderVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: ChatPayOrderVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoChatSessionVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: ChatSessionVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoGenTable = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: GenTable[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoGenTableColumn = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: GenTableColumn[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoKnowledgeAttachVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: KnowledgeAttachVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoKnowledgeFragmentVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: KnowledgeFragmentVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoKnowledgeInfoVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: KnowledgeInfoVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysConfigVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysConfigVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysDictDataVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysDictDataVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysDictTypeVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysDictTypeVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysLogininforVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysLogininforVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysNoticeStateVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysNoticeStateVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysNoticeVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysNoticeVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysOperLogVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysOperLogVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysOssConfigVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysOssConfigVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysOssVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysOssVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysPostVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysPostVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysRoleVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysRoleVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysTenantPackageVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysTenantPackageVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysUserOnline = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysUserOnline[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TableDataInfoSysUserVo = {
    /** 总记录数 */
    total?: number;
    /** 列表数据 */
    rows?: SysUserVo[];
    /** 消息状态码 */
    code?: number;
    /** 消息内容 */
    msg?: string;
  };

  type TaskConditionDTO = {
    ids?: string[];
  };

  type TenantListVo = {
    tenantId?: string;
    companyName?: string;
    domain?: string;
  };

  type tenantPackageMenuTreeselectParams = {
    /** 租户套餐ID */
    packageId: number;
  };

  type TextToSpeech = {
    model?: string;
    /** 音频声音源 */
    voice?: string;
    /** 输入内容 */
    input?: string;
    /** 速度调节，默认是1，取值范围0.25——4.0 */
    speed?: number;
    /** 输出音频文件格式 */
    response_format?: string;
  };

  type ToolCallFunction = {
    /** 方法名 */
    name?: string;
    /** 方法参数 */
    arguments?: string;
  };

  type ToolCalls = {
    /** The ID of the tool call. */
    id?: string;
    /** The type of the tool. Currently, only function is supported. */
    type?: string;
    function?: ToolCallFunction;
  };

  type translationByFileParams = {
    targetLanguage: string;
  };

  type TreeLong = {
    name?: { empty?: boolean };
    id?: number;
    parentId?: number;
    config?: TreeNodeConfig;
    weight?: Record<string, any>;
    empty?: boolean;
  };

  type TreeNodeConfig = {
    idKey?: string;
    parentIdKey?: string;
    weightKey?: string;
    nameKey?: string;
    childrenKey?: string;
    deep?: number;
  };

  type treeselectParams = {
    menu: SysMenuBo;
  };

  type unallocatedListParams = {
    user: SysUserBo;
    pageQuery: PageQuery;
  };

  type unlockParams = {
    userName: string;
  };

  type updatePactStatusParams = {
    /** 清单ID */
    pactId: number;
    /** 发布/停用标志 (1: 发布, 0: 停用) */
    listRelease: string;
  };

  type upload1Params = {
    bo: KnowledgeInfoUploadBo;
  };

  type UploadFileResponse = {
    bytes?: number;
    created_at?: number;
    filename?: string;
    id?: string;
    object?: string;
    url?: string;
  };

  type UserInfoVo = {
    /** 用户基本信息 */
    user?: SysUserVo;
    /** 菜单权限 */
    permissions?: string[];
    /** 角色权限 */
    roles?: string[];
  };

  type UserRequest = {
    /** 用户名称 */
    nickName: string;
  };

  type VisitorLoginBody = {
    code?: string;
    /** 登录类型(1.小程序访客 2.pc访客) */
    type?: string;
  };

  type WhisperResponse = {
    text?: string;
  };
}
