// @ts-ignore
/* eslint-disable */
import request from "@/utils/request";

/** 修改合同清单 修改合同清单 PUT /pactList */
export async function edit16(
  body: API.PactListBo,
  options?: { [key: string]: any }
) {
  return request<API.RVoid>("/pactList", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增合同清单，返回新增后的ID 新增合同清单，返回新增后的ID POST /pactList */
export async function add16(
  body: API.PactListBo,
  options?: { [key: string]: any }
) {
  return request<API.RLong>("/pactList", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除合同清单 删除合同清单 DELETE /pactList/${param0} */
export async function remove20(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remove20Params,
  options?: { [key: string]: any }
) {
  const { pactIds: param0, ...queryParams } = params;
  return request<API.RVoid>(`/pactList/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新清单状态 根据清单ID更新清单的发布/停用状态 PUT /pactList/${param0}/status */
export async function updatePactStatus(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updatePactStatusParams,
  options?: { [key: string]: any }
) {
  const { pactId: param0, ...queryParams } = params;
  return request<API.RVoid>(`/pactList/${param0}/status`, {
    method: "PUT",
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取所有合同清单 获取所有合同清单 GET /pactList/allList */
export async function allList(options?: { [key: string]: any }) {
  return request<API.RPactListVo>("/pactList/allList", {
    method: "GET",
    ...(options || {}),
  });
}
