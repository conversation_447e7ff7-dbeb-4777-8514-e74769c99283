<template>
	<div class="flex h-full bg-white dark:bg-[#101014]">
		<!-- 左侧栏 -->
		<div class="w-80 border-r border-gray-200 dark:border-gray-700 flex flex-col">
			<!-- 新建审查清单按钮 -->
			<div class="p-4 border-b border-gray-200 dark:border-gray-700">
				<button
					class="w-full px-4 py-3 rounded-lg text-white font-medium transition-all duration-300 hover:shadow-lg hover:scale-105 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
					@click="handleCreateChecklist">
					<div class="flex items-center justify-center space-x-2">
						<NIcon size="18">
							<AddOutline />
						</NIcon>
						<span>新建审查清单</span>
					</div>
				</button>
			</div>

			<!-- 搜索框 -->
			<div class="p-4 border-b border-gray-200 dark:border-gray-700">
				<NInput v-model:value="searchValue" placeholder="搜索清单列表" clearable>
					<template #prefix>
						<NIcon>
							<SearchOutline />
						</NIcon>
					</template>
				</NInput>
			</div>

			<!-- 树形列表 -->
			<div class="flex-1 overflow-y-auto tree-container">
				<NSpin :show="loading">
					<NTree :data="filteredTreeData" :selected-keys="selectedKeys" :expanded-keys="expandedKeys" selectable
						@update:selected-keys="handleSelectNode" @update:expanded-keys="handleExpandNode" />
				</NSpin>
			</div>
		</div>

		<!-- 右侧内容区 -->
		<div class="flex-1 flex flex-col h-full">
			<!-- 空状态 -->
			<div v-if="!selectedChecklist" class="flex-1 flex items-center justify-center text-gray-500">
				<div class="text-center">
					<NIcon size="48" class="mb-4">
						<DocumentTextOutline />
					</NIcon>
					<div>请从左侧列表选择清单</div>
				</div>
			</div>

			<!-- 清单详情 -->
			<div v-else class="flex-1 flex flex-col h-full">
				<!-- 清单头部信息 -->
				<div class="p-6 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
					<div class="flex justify-between items-start">
						<div class="flex-1">
							<div class="flex items-center space-x-2 mb-2">
								<h1 class="text-2xl font-bold">
									{{ selectedChecklist.title }}
								</h1>
								<NBadge :value="selectedChecklist.status === 'published' ? '已发布' : '停用'"
									:type="selectedChecklist.status === 'published' ? 'success' : 'warning'" />
							</div>
							<div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
								<span class="font-medium">清单名称：</span>{{ selectedChecklist.name }}
							</div>
							<div class="text-sm text-gray-600 dark:text-gray-400">
								<span class="font-medium">清单描述：</span>{{ selectedChecklist.description }}
							</div>
						</div>

						<!-- 已发布状态的操作按钮 -->
						<div v-if="selectedChecklist.status === 'published'" class="flex items-center space-x-3">
							<div class="flex items-center space-x-2">
								<label class="text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">审查立场：</label>
								<NSelect v-model:value="reviewStance" :options="currentStanceOptions" placeholder="选择审查立场"
									:style="{ width: '250px' }" />
							</div>
							<div class="flex space-x-2">
								<NButton @click="handleEditChecklist">停用</NButton>
								<NButton type="primary" @click="handleStartReview">发起审查</NButton>
							</div>
						</div>

						<!-- 编辑状态的操作按钮 -->
						<div v-else class="flex items-center space-x-3">
							<div class="flex items-center space-x-2">
								<label class="text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">展示填写示例：</label>
								<NSwitch v-model:value="showExample" />
							</div>
							<div class="flex space-x-2">
								<NButton @click="handleDeleteChecklist" type="error" ghost>删除</NButton>
								<NButton @click="handleSaveChecklist">保存</NButton>
								<NButton @click="handlePublishChecklist" type="primary">发布</NButton>
							</div>
						</div>
					</div>
				</div>

				<!-- 主要内容区 -->
				<div class="flex-1 flex min-h-0">
					<!-- 左侧条款列表 -->
					<div class="w-[20%] border-r border-gray-200 dark:border-gray-700 flex flex-col h-full">
						<div
							class="p-4 border-gray-200 dark:border-gray-700 font-medium flex-shrink-0 flex justify-between items-center">
							<span>条款列表</span>
							<NButton v-if="selectedChecklist.status === 'draft'" size="small" quaternary type="primary"
								@click="handleAddClause">
								+ 添加
							</NButton>
						</div>
						<div class="flex-1 overflow-y-auto min-h-0">
							<div v-for="clause in selectedChecklist.clauses" :key="clause.id"
								class="p-4 border-b border-gray-100 dark:border-gray-800">
								<div class="mb-1">{{ clause.title }}</div>
							</div>
						</div>
					</div>

					<!-- 右侧条款详情 -->
					<div :class="showExample ? 'w-[50%]' : 'w-[80%]'"
						class="flex flex-col h-full border-r border-gray-200 dark:border-gray-700">
						<div class="flex-1 overflow-y-auto p-4 min-h-0">
							<NCollapse v-model:expanded-names="collapseExpandedNames">
								<NCollapseItem v-for="clause in selectedChecklist.clauses" :key="clause.id"
									:name="clause.id.toString()">
									<template #header>
										<div class="flex justify-between items-center w-full">
											<div v-if="selectedChecklist.status === 'published'">
												{{ clause.title }}
											</div>
											<div v-else class="flex-1 mr-4">
												<NInput v-model:value="clause.title" placeholder="请输入条款标题" @click.stop />
											</div>
											<!-- 删除按钮 -->
											<div v-if="selectedChecklist.status === 'draft'" class="flex-shrink-0">
												<NPopover trigger="click" placement="bottom">
													<template #trigger>
														<NButton size="tiny" type="error" ghost circle @click.stop>
															<template #icon>
																<NIcon>
																	<TrashOutline />
																</NIcon>
															</template>
														</NButton>
													</template>
													<div class="text-center">
														<div class="mb-2">确定删除该条款下的所有内容吗？</div>
														<NSpace>
															<NButton size="small" @click="handleDeleteClause(clause.id)">
																确定
															</NButton>
														</NSpace>
													</div>
												</NPopover>
											</div>
										</div>
									</template>

									<div class="space-y-4 border-solid border border-slate-200 p-5 rounded" style="margin-top: -20px;">
										<!-- 条款说明 -->
										<div>
											<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
												<span class="text-red-500">*</span> 条款说明
											</h4>
											<div v-if="selectedChecklist.status === 'published'">
												<div
													class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed p-3 bg-gray-50 dark:bg-gray-800 rounded">
													{{ clause.description }}
												</div>
											</div>
											<div v-else>
												<NInput v-model:value="clause.description" type="textarea" :rows="3" placeholder="请输入条款说明" />
											</div>
										</div>

										<!-- 风险点 -->
										<div>
											<div class="flex justify-between items-center mb-2">
												<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
													<span class="text-red-500">*</span> 风险点
												</h4>
												<NButton v-if="selectedChecklist.status === 'draft'" size="small" quaternary type="primary"
													@click="handleAddRisk(clause)">
													+ 添加自定义风险点
												</NButton>
											</div>

											<div class="space-y-2">
												<div v-for="risk in clause.risks" :key="risk.id" class="p-3 border rounded relative group">
													<!-- 已发布状态：只读显示 -->
													<div v-if="selectedChecklist.status === 'published'">
														<div v-if="risk.enabled" class="font-medium mb-1">
															{{ risk.title }}
														</div>
														<div v-if="risk.enabled" class="text-sm text-gray-600 dark:text-gray-400">
															{{ risk.description }}
														</div>
													</div>

													<!-- 编辑状态：可编辑 -->
													<div v-else>
														<div class="flex items-start space-x-2">
															<NCheckbox v-model:checked="risk.enabled" class="mt-1" />
															<div class="flex-1">
																<div class="font-medium mb-1 cursor-pointer" @click="handleEditRisk(risk)">
																	{{ risk.title }}
																</div>
																<div class="text-sm">
																	{{ risk.description }}
																</div>
															</div>
															<div class="opacity-0 group-hover:opacity-100 transition-opacity">
																<NButton size="tiny" type="error" ghost circle
																	@click="handleDeleteRisk(clause.id, risk.id)">
																	<template #icon>
																		<NIcon>
																			<TrashOutline />
																		</NIcon>
																	</template>
																</NButton>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</NCollapseItem>
							</NCollapse>
						</div>
					</div>

					<!-- 填写示例区域 -->
					<div v-if="showExample" class="w-[30%] flex flex-col h-full">
						<div class="p-4 border-gray-200 dark:border-gray-700 font-medium flex-shrink-0">
							填写示例
						</div>
						<div class="flex-1 overflow-y-auto p-4 min-h-0">
							<div class="space-y-4">
								<div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded">
									<h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">
										条款名称：填写具体的条款名称
									</h4>
									<p class="text-sm text-blue-700 dark:text-blue-300">
										例：标的物条款
									</p>
								</div>

								<div class="bg-green-50 dark:bg-green-900/20 p-4 rounded">
									<h4 class="font-medium text-green-800 dark:text-green-200 mb-2">
										条款说明：该类条款的描述性说明，以便于模型定位该条款再合同中的内容
									</h4>
									<p class="text-sm text-green-700 dark:text-green-300">
										例：标的物条款明确描述设备采购合同的标的物，包括标的物的名称、品牌型号、规格（功能要求、核心技术参数）、数量（重量）、价格（单价）、配件、附属服务、质量标准等内容，标的物条款的最终目的是明确交付设备的数量、品牌、型号等内容。
									</p>
								</div>

								<div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded">
									<h4 class="font-medium text-orange-800 dark:text-orange-200 mb-2">
										风险点：该类条款中需要审查的一种风险名称及其具体说明，让模型能够更好地理解、识别合同条款中存在的风险点
									</h4>
									<div class="space-y-2">
										<div class="text-sm text-orange-700 dark:text-orange-300">
											<div class="font-medium">例：使用非标准化的计量单位</div>
											<div class="ml-4">
												标的物的数量使用非标准化的计量单位，如"包、箱、袋、捆、车"等；
											</div>
										</div>
										<div class="text-sm text-orange-700 dark:text-orange-300">
											<div class="font-medium">例：标的物重要信息缺失</div>
											<div class="ml-4">标的物信息中缺失"名称、规格、数量、价格"；或未将该信息作为附件；或仅约定"详见附件""详见xx协议"，实际没有附件或附件中信息仍缺失。</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 新建审查清单弹窗 -->
		<NModal v-model:show="showCreateModal" preset="dialog" title="新建审查清单" :style="{ width: '600px' }">
			<NForm ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="150px" class="mt-4">
				<!-- 清单名称 -->
				<NFormItem label="清单名称：" path="name">
					<NInput v-model:value="formData.name" placeholder="请输入清单名称" maxlength="20" show-count />
				</NFormItem>

				<!-- 清单创建方式 -->
				<NFormItem label="清单创建方式：" path="createType">
					<NRadioGroup v-model:value="formData.createType">
						<NRadioButton value="fromExisting">从已有清单创建</NRadioButton>
						<NRadioButton value="fromBlank">从空白清单创建</NRadioButton>
					</NRadioGroup>
				</NFormItem>

				<!-- 选择模板 -->
				<NFormItem v-if="formData.createType === 'fromExisting'" label="选择模板：" path="selectedTemplate">
					<NSelect v-model:value="formData.selectedTemplate" :options="templateOptions" placeholder="请选择模板" />
				</NFormItem>

				<!-- 审查立场 -->
				<NFormItem v-if="formData.createType === 'fromExisting'" label="审查立场：" path="reviewStance">
					<NSelect v-model:value="formData.reviewStance" :options="templateStanceOptions" placeholder="请选择审查立场" />
				</NFormItem>

				<!-- 清单描述 -->
				<NFormItem label="清单描述：" path="description">
					<NInput v-model:value="formData.description" type="textarea" :rows="3" placeholder="请输入清单描述" />
				</NFormItem>
			</NForm>

			<template #action>
				<NSpace>
					<NButton @click="handleCancelCreate">取消</NButton>
					<NButton type="primary" @click="handleConfirmCreate">确定</NButton>
				</NSpace>
			</template>
		</NModal>

		<!-- 添加条款弹窗 -->
		<NModal v-model:show="showAddClauseModal" preset="dialog" title="添加条款" :style="{ width: '500px' }">
			<NForm ref="clauseFormRef" :model="clauseFormData" :rules="clauseFormRules" label-placement="left"
				label-width="100px" class="mt-4">
				<NFormItem label="条款名称：" path="title">
					<NInput v-model:value="clauseFormData.title" placeholder="请输入条款名称" />
				</NFormItem>
				<NFormItem label="条款说明：" path="description">
					<NInput v-model:value="clauseFormData.description" type="textarea" :rows="3" placeholder="请输入条款说明" />
				</NFormItem>
			</NForm>

			<template #action>
				<NSpace>
					<NButton @click="showAddClauseModal = false">取消</NButton>
					<NButton type="primary" @click="handleConfirmAddClause">确定</NButton>
				</NSpace>
			</template>
		</NModal>

		<!-- 添加风险点弹窗 -->
		<NModal v-model:show="showAddRiskModal" preset="dialog" title="添加风险点" :style="{ width: '500px' }">
			<NForm ref="riskFormRef" :model="riskFormData" :rules="riskFormRules" label-placement="left" label-width="100px"
				class="mt-4">
				<NFormItem label="风险点名称：" path="title">
					<NInput v-model:value="riskFormData.title" placeholder="请输入风险点名称" />
				</NFormItem>
				<NFormItem label="风险点说明：" path="description">
					<NInput v-model:value="riskFormData.description" type="textarea" :rows="3" placeholder="请输入风险点说明" />
				</NFormItem>
			</NForm>

			<template #action>
				<NSpace>
					<NButton @click="showAddRiskModal = false">取消</NButton>
					<NButton type="primary" @click="handleConfirmAddRisk">确定</NButton>
				</NSpace>
			</template>
		</NModal>

		<!-- 编辑风险点弹窗 -->
		<NModal v-model:show="showEditRiskModal" preset="dialog" title="编辑风险点" :style="{ width: '600px' }">
			<NForm ref="riskFormRef" :model="riskFormData" :rules="riskFormRules" label-placement="left" label-width="120px"
				class="mt-4">
				<NFormItem label="风险点名称：" path="title">
					<NInput v-model:value="riskFormData.title" placeholder="请输入风险点名称" maxlength="50" show-count />
				</NFormItem>

				<NFormItem label="风险点说明：" path="description">
					<NInput v-model:value="riskFormData.description" type="textarea" placeholder="请输入风险点说明" :rows="4" />
				</NFormItem>
			</NForm>

			<template #action>
				<div class="flex justify-end space-x-2">
					<NButton @click="showEditRiskModal = false">取消</NButton>
					<NButton type="primary" @click="handleConfirmEditRisk">确定</NButton>
				</div>
			</template>
		</NModal>
	</div>
</template>

<script setup lang="ts">
import {
	AddOutline,
	DocumentTextOutline,
	SearchOutline,
	TrashOutline,
} from "@vicons/ionicons5";
import {
	NButton,
	NCollapse,
	NCollapseItem,
	NIcon,
	NInput,
	NSelect,
	NTree,
	NModal,
	NForm,
	NFormItem,
	NRadioGroup,
	NRadioButton,
	NBadge,
	NSwitch,
	NCheckbox,
	NPopover,
	NSpace,
	NSpin,
	useMessage,
	useDialog,
} from "naive-ui";
import { computed, ref, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { allList, add16, edit16, remove20, updatePactStatus } from "@/services/api/hetongqingdan";

const message = useMessage();
const dialog = useDialog();
const router = useRouter();

// 加载状态
const loading = ref(false);

// 弹窗相关
const showCreateModal = ref(false);
const showAddClauseModal = ref(false);
const showAddRiskModal = ref(false);
const showEditRiskModal = ref(false);
const formRef = ref();
const clauseFormRef = ref();
const riskFormRef = ref();

// 表单数据
const formData = ref({
	name: "",
	createType: "fromExisting",
	selectedTemplate: "",
	reviewStance: "",
	description: "",
});

const clauseFormData = ref({
	title: "",
	description: "",
});

const riskFormData = ref({
	title: "",
	description: "",
});

// 当前编辑的条款
const currentClause = ref<any>(null);
const currentEditingRisk = ref<any>(null);

// 表单验证规则
const formRules = {
	name: [
		{ required: true, message: "请输入清单名称", trigger: "blur" },
		{ max: 20, message: "清单名称不能超过20个字符", trigger: "blur" },
	],
	createType: [
		{ required: true, message: "请选择创建方式", trigger: "change" },
	],
	selectedTemplate: [
		{
			required: true,
			message: "请选择模板",
			trigger: "change",
			validator: () => {
				if (formData.value.createType === "fromExisting" && !formData.value.selectedTemplate) {
					return new Error("请选择模板");
				}
				return true;
			},
		},
	],
	reviewStance: [
		{
			required: true,
			message: "请选择审查立场",
			trigger: "change",
			validator: () => {
				if (formData.value.createType === "fromExisting" && !formData.value.reviewStance) {
					return new Error("请选择审查立场");
				}
				return true;
			},
		},
	],
};

const clauseFormRules = {
	title: [
		{ required: true, message: "请输入条款名称", trigger: "blur" },
		{ max: 50, message: "条款名称不能超过50个字符", trigger: "blur" },
	],
	description: [
		{ required: true, message: "请输入条款说明", trigger: "blur" },
	],
};

const riskFormRules = {
	title: [
		{ required: true, message: "请输入风险点名称", trigger: "blur" },
		{ max: 50, message: "风险点名称不能超过50个字符", trigger: "blur" },
	],
	description: [
		{ required: true, message: "请输入风险点说明", trigger: "blur" },
	],
};

// 搜索值
const searchValue = ref("");

// 选中的节点
const selectedKeys = ref<string[]>([]);

// 展开的节点
const expandedKeys = ref<string[]>(["preset", "my"]);

// 审查立场
const reviewStance = ref("");

// 审查立场选项
const reviewStanceOptions = [
	{
		label: "甲方立场",
		value: "party_a",
	},
	{
		label: "乙方立场",
		value: "party_b",
	},
	{
		label: "中立立场",
		value: "neutral",
	},
];

// 选中的清单
const selectedChecklist = ref<any>(null);

// 编辑状态
const showExample = ref(false);

// Collapse展开状态
const collapseExpandedNames = ref<string[]>([]);

// 数据转换函数
const transformPactListToTreeData = (pactListVo: API.PactListVo) => {
	const userPactList = pactListVo.userPactList || [];
	const sysPactList = pactListVo.sysPactList || [];

	const transformPactItem = (pact: API.PactListVo, keyPrefix: string) => ({
		key: `${keyPrefix}-${pact.pactId}`,
		label: pact.listName || '未命名清单',
		type: "checklist",
		data: {
			pactId: pact.pactId,
			title: pact.listName || '未命名清单',
			name: pact.listName || '未命名清单',
			description: pact.listDescription || '暂无描述',
			status: pact.listRelease === '1' ? 'published' : 'draft',
			listSource: pact.listSource,
			listCreate: pact.listCreate,
			listCreateId: pact.listCreateId,
			dictValue: pact.dictValue,
			dictLabel: pact.dictLabel,
			pactListStanceList: pact.pactListStanceList || [], // 添加审查立场列表
			clauses: generateSampleClauses(), // 生成示例条款数据
			createTime: pact.createTime,
			updateTime: pact.updateTime
		}
	});

	return [
		{
			key: "preset",
			label: "预置清单",
			children: sysPactList.map(pact => transformPactItem(pact, 'preset'))
		},
		{
			key: "my",
			label: "我的清单",
			children: userPactList.map(pact => transformPactItem(pact, 'my'))
		}
	];
};

// 生成示例条款数据（实际应该从后端获取）
const generateSampleClauses = () => {
	return [
		{
			id: 1,
			title: "主体信息条款",
			category: "合同主体",
			description: "主体信息条款应当明确规定合同当事人的身份、资质等基本信息。",
			risks: [
				{
					id: 1,
					title: "合同主体身份不明",
					description: "缺少对合同主体身份的明确约定，可能导致合同履行困难",
					enabled: true,
				},
				{
					id: 2,
					title: "主体资格审查不充分",
					description: "未对合同主体的资格进行充分审查，可能存在履约风险",
					enabled: true,
				},
			],
		},
		{
			id: 2,
			title: "标的物条款",
			category: "标的物",
			description: "标的物条款应当详细描述合同标的物的具体信息和要求。",
			risks: [
				{
					id: 3,
					title: "标的物描述不清",
					description: "标的物信息不够详细可能导致纠纷",
					enabled: true,
				},
			],
		},
	];
};

// 定义树形数据类型
interface TreeNode {
	key: string;
	label: string;
	type?: string;
	children?: TreeNode[];
	data?: any;
}

// 树形数据
const treeData = ref<TreeNode[]>([
	{
		key: "preset",
		label: "预置清单",
		children: []
	},
	{
		key: "my",
		label: "我的清单",
		children: []
	}
]);

// 获取合同清单数据
const fetchPactLists = async () => {
	try {
		loading.value = true;
		const response = await allList() as any;
		if (response.code === 200 && response.data) {
			treeData.value = transformPactListToTreeData(response.data);
		} else {
			message.error(response.msg || '获取清单数据失败');
		}
	} catch (error) {
		console.error('获取清单数据失败:', error);
		message.error('获取清单数据失败');
	} finally {
		loading.value = false;
	}
};

// 模板选项（预置清单）
const templateOptions = computed(() => {
	const presetChecklists = treeData.value.find((item) => item.key === "preset")?.children || [];
	return presetChecklists.map((item) => ({
		label: item.label,
		value: item.key,
	}));
});

// 当前选中清单的审查立场选项
const currentStanceOptions = computed(() => {
	if (!selectedChecklist.value || !selectedChecklist.value.pactListStanceList) {
		return [];
	}
	return selectedChecklist.value.pactListStanceList.map((stance: any) => ({
		label: stance.dictLabel,
		value: stance.dictValue,
	}));
});

// 模板的审查立场选项
const templateStanceOptions = computed(() => {
	if (formData.value.createType !== 'fromExisting' || !formData.value.selectedTemplate) {
		return [];
	}

	const presetChecklists = treeData.value.find((item) => item.key === "preset")?.children || [];
	const selectedTemplate = presetChecklists.find((item) => item.key === formData.value.selectedTemplate);

	if (!selectedTemplate || !selectedTemplate.data.pactListStanceList) {
		return [];
	}

	return selectedTemplate.data.pactListStanceList.map((stance: any) => ({
		label: stance.dictLabel,
		value: stance.dictValue,
	}));
});

// 过滤后的树形数据
const filteredTreeData = computed(() => {
	if (!searchValue.value) return treeData.value;

	const filterNode = (nodes: any[]): any[] => {
		return nodes.reduce((acc, node) => {
			if (node.label.includes(searchValue.value)) {
				acc.push(node);
			} else if (node.children) {
				const filteredChildren = filterNode(node.children);
				if (filteredChildren.length > 0) {
					acc.push({
						...node,
						children: filteredChildren,
					});
				}
			}
			return acc;
		}, []);
	};

	return filterNode(treeData.value);
});

// 处理节点选择
const handleSelectNode = (keys: string[]) => {
	selectedKeys.value = keys;
	if (keys.length > 0) {
		const selectedKey = keys[0];
		const findNode = (nodes: any[]): any => {
			for (const node of nodes) {
				if (node.key === selectedKey && node.type === "checklist") {
					return node.data;
				}
				if (node.children) {
					const found = findNode(node.children);
					if (found) return found;
				}
			}
			return null;
		};

		const nodeData = findNode(treeData.value);
		if (nodeData) {
			selectedChecklist.value = nodeData;
			// 自动展开所有条款面板
			collapseExpandedNames.value = nodeData.clauses.map((clause: any) => clause.id.toString());
			// 设置默认审查立场（选择第一个可用的立场）
			if (nodeData.pactListStanceList && nodeData.pactListStanceList.length > 0) {
				reviewStance.value = nodeData.pactListStanceList[0].dictValue;
			} else {
				reviewStance.value = "";
			}
		}
	}
};

// 处理节点展开
const handleExpandNode = (keys: string[]) => {
	expandedKeys.value = keys;
};

// 监听模板选择变化，重置审查立场
watch(() => formData.value.selectedTemplate, () => {
	formData.value.reviewStance = "";
});

// 监听创建方式变化，重置相关字段
watch(() => formData.value.createType, () => {
	formData.value.selectedTemplate = "";
	formData.value.reviewStance = "";
});

// 初始化数据
onMounted(() => {
	fetchPactLists();
});

// 处理创建清单
const handleCreateChecklist = () => {
	showCreateModal.value = true;
	// 重置表单数据
	formData.value = {
		name: "",
		createType: "fromExisting",
		selectedTemplate: "",
		reviewStance: "",
		description: "",
	};
};

// 处理取消创建
const handleCancelCreate = () => {
	showCreateModal.value = false;
};

// 处理确认创建
const handleConfirmCreate = async () => {
	try {
		await formRef.value?.validate();

		loading.value = true;

		// 获取选中的审查立场信息
		let selectedStance = null;
		let selectedTemplate = null;
		let dictValueList: string[] = [];

		if (formData.value.createType === "fromExisting" && formData.value.selectedTemplate) {
			const presetChecklists = treeData.value.find((item) => item.key === "preset")?.children || [];
			selectedTemplate = presetChecklists.find((item) => item.key === formData.value.selectedTemplate);

			if (selectedTemplate && selectedTemplate.data.pactListStanceList) {
				// 获取所选择的模板下所有的pactListStanceList的dictValue
				dictValueList = selectedTemplate.data.pactListStanceList.map((stance: any) => stance.dictValue);

				// 获取选中的审查立场信息
				selectedStance = selectedTemplate.data.pactListStanceList.find((stance: any) => stance.dictValue === formData.value.reviewStance);
			}
		}

		const pactListBo: API.PactListBo = {
			pactId: 0, // 新增时传0，表示新增
			listName: formData.value.name,
			listDescription: formData.value.description,
			listSource: "1", // 用户创建
			listCreate: formData.value.createType === "fromExisting" ? "0" : "1",
			listCreateId: formData.value.createType === "fromExisting" ? getTemplateId(formData.value.selectedTemplate) : undefined,
			dictValue: selectedStance?.dictValue, // 传选择的审查立场的dictValue
			dictLabel: selectedStance?.dictLabel, // 传选择的审查立场的dictLabel
			dictValueList: dictValueList, // 传所选择的模板下所有的pactListStanceList的dictValue
			// 删掉listRelease参数
		};

		const response = await add16(pactListBo) as any;
		if (response.code === 200) {
			message.success("清单创建成功！");
			showCreateModal.value = false;
			// 重新获取数据
			await fetchPactLists();
		} else {
			message.error(response.msg || '创建清单失败');
		}
	} catch (error) {
		console.error("创建清单失败:", error);
		message.error('创建清单失败，请稍后重试');
	} finally {
		loading.value = false;
	}
};

// 获取模板ID
const getTemplateId = (templateKey: string): number | undefined => {
	const presetChecklists = treeData.value.find((item) => item.key === "preset")?.children || [];
	const template = presetChecklists.find((item) => item.key === templateKey);
	return template?.data?.pactId;
};

// 处理保存清单
const handleSaveChecklist = async () => {
	if (!selectedChecklist.value) {
		message.error("请先选择清单");
		return;
	}

	try {
		const pactListBo: API.PactListBo = {
			pactId: selectedChecklist.value.pactId, // 编辑时传递pactId
			listName: selectedChecklist.value.name,
			listDescription: selectedChecklist.value.description,
			listSource: selectedChecklist.value.listSource,
			listCreate: selectedChecklist.value.listCreate,
			listCreateId: selectedChecklist.value.listCreateId,
			dictValue: selectedChecklist.value.dictValue,
			dictLabel: selectedChecklist.value.dictLabel,
			dictValueList: selectedChecklist.value.pactListStanceList?.map((stance: any) => stance.dictValue) || [],
			// 删掉listRelease参数
		};

		const response = await edit16(pactListBo) as any;
		if (response.code === 200) {
			message.success("清单保存成功！");
			// 重新获取数据
			await fetchPactLists();
		} else {
			message.error(response.msg || '保存清单失败');
		}
	} catch (error) {
		console.error('保存清单失败:', error);
		message.error('保存清单失败');
	}
};

// 处理发布清单
const handlePublishChecklist = async () => {
	if (!selectedChecklist.value) {
		message.error("请先选择清单");
		return;
	}

	try {
		const response = await updatePactStatus({
			pactId: selectedChecklist.value.pactId,
			listRelease: '1'
		}) as any;

		if (response.code === 200) {
			selectedChecklist.value.status = "published";
			message.success("清单发布成功！");
			// 重新获取数据
			await fetchPactLists();
		} else {
			message.error(response.msg || '发布清单失败');
		}
	} catch (error) {
		console.error('发布清单失败:', error);
		message.error('发布清单失败');
	}
};

// 处理编辑清单（停用）
const handleEditChecklist = async () => {
	if (!selectedChecklist.value) {
		message.error("请先选择清单");
		return;
	}

	try {
		const response = await updatePactStatus({
			pactId: selectedChecklist.value.pactId,
			listRelease: '0'
		}) as any;

		if (response.code === 200) {
			selectedChecklist.value.status = "draft";
			message.success("清单已停用！");
			// 重新获取数据
			await fetchPactLists();
		} else {
			message.error(response.msg || '停用清单失败');
		}
	} catch (error) {
		console.error('停用清单失败:', error);
		message.error('停用清单失败');
	}
};

// 处理删除清单
const handleDeleteChecklist = () => {
	if (!selectedChecklist.value) {
		message.error("请先选择清单");
		return;
	}

	dialog.warning({
		title: "确认删除",
		content: "确定要删除这个清单吗？删除后无法恢复。",
		positiveText: "确定",
		negativeText: "取消",
		onPositiveClick: async () => {
			try {
				const response = await remove20({
					pactIds: [selectedChecklist.value.pactId]
				}) as any;

				if (response.code === 200) {
					selectedChecklist.value = null;
					selectedKeys.value = [];
					message.success("清单删除成功！");
					// 重新获取数据
					await fetchPactLists();
				} else {
					message.error(response.msg || '删除清单失败');
				}
			} catch (error) {
				console.error('删除清单失败:', error);
				message.error('删除清单失败');
			}
		},
	});
};

// 处理添加条款
const handleAddClause = () => {
	showAddClauseModal.value = true;
	clauseFormData.value = {
		title: "",
		description: "",
	};
};

// 处理确认添加条款
const handleConfirmAddClause = async () => {
	try {
		await clauseFormRef.value?.validate();

		const newClause = {
			id: Date.now(), // 临时ID，实际应该由后端生成
			title: clauseFormData.value.title,
			description: clauseFormData.value.description,
			category: "自定义",
			risks: [],
		};

		selectedChecklist.value.clauses.push(newClause);
		// 自动展开新添加的条款
		collapseExpandedNames.value.push(newClause.id.toString());

		showAddClauseModal.value = false;
		message.success("条款添加成功！");
	} catch (error) {
		console.log("表单验证失败:", error);
	}
};

// 删除条款
const handleDeleteClause = (clauseId: number) => {
	const index = selectedChecklist.value.clauses.findIndex((c: any) => c.id === clauseId);
	if (index > -1) {
		selectedChecklist.value.clauses.splice(index, 1);
		// 从展开状态中移除删除的条款
		const expandedIndex = collapseExpandedNames.value.indexOf(clauseId.toString());
		if (expandedIndex > -1) {
			collapseExpandedNames.value.splice(expandedIndex, 1);
		}
		message.success("条款删除成功！");
	}
};

// 处理添加风险点
const handleAddRisk = (clause: any) => {
	currentClause.value = clause;
	showAddRiskModal.value = true;
	riskFormData.value = {
		title: "",
		description: "",
	};
};

// 处理确认添加风险点
const handleConfirmAddRisk = async () => {
	try {
		await riskFormRef.value?.validate();

		const newRisk = {
			id: Date.now(), // 临时ID，实际应该由后端生成
			title: riskFormData.value.title,
			description: riskFormData.value.description,
			enabled: true,
		};

		currentClause.value.risks.push(newRisk);
		showAddRiskModal.value = false;
		message.success("风险点添加成功！");
	} catch (error) {
		console.log("表单验证失败:", error);
	}
};

// 处理确认编辑风险点
const handleConfirmEditRisk = async () => {
	try {
		await riskFormRef.value?.validate();

		if (currentEditingRisk.value) {
			currentEditingRisk.value.title = riskFormData.value.title;
			currentEditingRisk.value.description = riskFormData.value.description;
			showEditRiskModal.value = false;
			message.success("风险点修改成功！");
		}
	} catch (error) {
		console.log("表单验证失败:", error);
	}
};

// 编辑风险点
const handleEditRisk = (risk: any) => {
	riskFormData.value = {
		title: risk.title,
		description: risk.description,
	};
	currentEditingRisk.value = risk;
	showEditRiskModal.value = true;
};

// 删除风险点
const handleDeleteRisk = (clauseId: number, riskId: number) => {
	const clause = selectedChecklist.value.clauses.find((c: any) => c.id === clauseId);
	if (!clause) return;

	const index = clause.risks.findIndex((r: any) => r.id === riskId);
	if (index > -1) {
		clause.risks.splice(index, 1);
		message.success("风险点删除成功！");
	}
};

// 发起审查
const handleStartReview = () => {
	if (!selectedChecklist.value) {
		message.error("请先选择清单");
		return;
	}

	if (!reviewStance.value) {
		message.error("请先选择审查立场");
		return;
	}

	// 跳转到合同审查页面，并传递参数
	router.push({
		path: "/contract/review",
		query: {
			scenario: selectedChecklist.value.name,
			stance: reviewStance.value,
		},
	});
};
</script>

<style scoped>
:deep(.n-collapse-item__header-main) {
	padding: 10px;
	background-color: rgb(246, 247, 251);
	border-radius: 5px;
	border: 1px solid #e2e8f0;
}

:deep(.n-collapse .n-collapse-item:not(:first-child)) {
	border-top: none
}

/* 树形组件样式优化 */
.tree-container :deep(.n-tree-node-content) {
	padding: 8px 12px;
	display: flex;
	align-items: center;
}

.tree-container :deep(.n-tree-node-content:hover) {
	background-color: var(--n-node-color-hover);
}

.tree-container :deep(.n-tree-node-switcher) {
	display: flex;
	align-items: center;
	justify-content: center;
}

.tree-container :deep(.n-tree-node-content__text) {
	display: flex;
	align-items: center;
}

/* 确保箭头和文字垂直居中对齐 */
.tree-container :deep(.n-tree-node) {
	display: flex;
	align-items: center;
}

.tree-container :deep(.n-tree-node-indent) {
	display: flex;
	align-items: center;
}
</style>
