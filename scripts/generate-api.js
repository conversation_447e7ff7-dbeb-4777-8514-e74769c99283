#!/usr/bin/env node

const { generateService } = require("@umijs/openapi");
const fs = require("fs");
const path = require("path");

// 从配置文件读取配置
const config = require("../api.config.js");

console.log("🚀 开始生成 API 接口文件...");
console.log(`📡 Swagger 地址: ${config.swaggerUrl}`);

async function generateApi() {
	try {
		let schemaPath = config.swaggerUrl;

		await generateService({
			// 从配置文件读取 Swagger 文档地址
			schemaPath,

			// 从配置文件读取生成配置
			...config.generate,
		});

		console.log("✅ API 接口文件生成完成！");
		console.log(`📁 生成位置: ${config.generate.serversPath}/`);
		console.log("📋 生成的文件包括:");
		console.log("  - typings.d.ts (TypeScript 类型定义)");
		console.log("  - [controller].ts (各个控制器的接口文件)");
		console.log("  - index.ts (统一导出)");
	} catch (error) {
		console.error("❌ 生成失败:", error.message);

		// 如果是网络错误，提供手动下载提示
		if (
			error.message.includes("fetch") ||
			error.message.includes("ENOTFOUND") ||
			error.message.includes("EHOSTUNREACH") ||
			error.message.includes("FetchError")
		) {
			console.log("");
			console.log("💡 网络连接失败，请手动下载 OpenAPI 文档:");
			console.log(`1. 访问: ${config.swaggerUrl}`);
			console.log(
				"2. 将返回的 JSON 内容保存为项目根目录下的 openapi.json 文件"
			);
			console.log("3. 重新运行: pnpm run openapi");
			console.log("");
		}

		process.exit(1);
	}
}

generateApi();
